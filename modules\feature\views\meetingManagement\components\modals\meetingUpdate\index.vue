<!-- 用于创建/编辑会议 -->
<template>
    <div>
        <el-dialog
            :title="title"
            :visible.sync="dialogVisible"
            width="95%"
            custom-class="feature-meeting--meeting-update"
            :before-close="reset"
        >
            <el-form
                ref="form"
                :model="form"
                class="form"
                size="mini"
                label-width="125px"
            >
                <el-form-item
                    label="会议名称"
                    :rules="required"
                    prop="meetingTitle"
                >
                    <div class="flex">
                        <el-input
                            v-model="form.meetingTitle"
                            placeholder="请输入会议名称"
                            style="width: 80%"
                            maxlength="100"
                        ></el-input>
                        <div
                            v-if="!isEdit"
                            class="copy-icon-container"
                            title="复制会议"
                        >
                            <svg-icon
                                class="copy-icon"
                                icon-class="feature-meeting-copy"
                                @click="handleMeetingCopy"
                            ></svg-icon>
                        </div>
                    </div>
                </el-form-item>

                <div class="flex">
                    <el-form-item
                        label="是否首次会议"
                        :rules="required"
                        prop="isFirst"
                        class="is-first"
                    >
                        <el-radio-group
                            v-model="form.isFirst"
                            @input="form.associateMeetingId = ''"
                        >
                            <el-radio :label="true">是</el-radio>
                            <el-radio :label="false">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="关联首次会议"
                        v-if="form.isFirst === false"
                        :rules="required"
                        prop="associateMeetingId"
                        class="related-meeting-form"
                    >
                        <el-select
                            v-model="form.associateMeetingId"
                            placeholder="请选择上次会议"
                            class="related-meeting-input"
                            filterable
                        >
                            <el-option
                                v-for="item in relatedMeetingOptions"
                                :key="item.meetingId"
                                :label="item.meetingTitle"
                                :value="item.meetingId"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div class="flex">
                    <el-form-item
                        label="会议类型"
                        :rules="required"
                        prop="meetingType"
                        style="margin-bottom: 12px; width: 50%"
                    >
                        <el-select
                            v-model="form.meetingType"
                            placeholder="请选择会议类型"
                            @input="handleMeetingTypeChange"
                        >
                            <el-option
                                v-for="item in CONSTANTS.MEETING_TYPE"
                                :key="item"
                                :label="item"
                                :value="item"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="评审组织形式"
                        v-if="meetingClass === '评审会议'"
                        :rules="required"
                        prop="organizeForm"
                        style="width: 50%"
                    >
                        <el-radio-group
                            v-model="form.organizeForm"
                            @input="handleIsOnlineChange"
                        >
                            <el-radio label="线下"></el-radio>
                            <el-radio label="线上"></el-radio>
                        </el-radio-group>
                    </el-form-item>
                </div>
                <div class="flex meeting-material">
                    <el-form-item
                        label="会议材料"
                        :rules="required"
                        prop="materialsGrantFlag"
                        style="width: 50%; margin-bottom: 18px"
                    >
                        <el-radio-group
                            v-model="form.materialsGrantFlag"
                            @input="form.materialsGrantMode = ''"
                        >
                            <el-radio label="未发放" value="未发放"></el-radio>
                            <el-radio label="已发放" value="已发放"></el-radio>
                            <el-radio label="无材料" value="无材料"></el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item
                        label="材料发放途径"
                        v-if="form.materialsGrantFlag === '已发放'"
                        :rules="required"
                        prop="materialsGrantMode"
                    >
                        <el-select
                            v-model="form.materialsGrantMode"
                            placeholder="请选择材料发放途径"
                            @input="
                                () => {
                                    form.meetingMaterials = '';
                                    form.materialsGrantPlan = '';
                                }
                            "
                        >
                            <el-option
                                label="邮件发放"
                                value="邮件发放"
                            ></el-option>
                            <el-option
                                label="网上共享"
                                value="网上共享"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="材料发放计划"
                        v-if="form.materialsGrantFlag === '未发放'"
                        prop="materialsGrantPlan"
                        style="width: 50%"
                    >
                        <el-input
                            v-model="form.materialsGrantPlan"
                            placeholder="请输入材料发放计划"
                            style="width: 100%"
                        ></el-input>
                    </el-form-item>
                </div>
                <el-form-item
                    label="共享地址"
                    v-if="form.materialsGrantMode === '网上共享'"
                    :rules="required"
                    prop="meetingMaterials"
                >
                    <el-input v-model="form.meetingMaterials"></el-input>
                </el-form-item>

                <div class="flex">
                    <el-form-item label="会议时间" style="width: 50%">
                        <el-date-picker
                            v-model="form.date"
                            type="date"
                            placeholder="请选择日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                            clearable
                            style="width: 180px"
                            :disabled="form.organizeForm === '线上'"
                            @change="
                                () =>
                                    (this.isHideSendingButton =
                                        this.handleIsHideSendingMailButton())
                            "
                        ></el-date-picker>
                        <el-time-picker
                            v-if="form.organizeForm !== '线上'"
                            class="time-picker"
                            size="small"
                            is-range
                            v-model="form.timeRange"
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            placeholder="选择时间范围"
                            format="HH:mm"
                            value-format="HH:mm"
                            clearable
                            @change="
                                () =>
                                    (this.isHideSendingButton =
                                        this.handleIsHideSendingMailButton())
                            "
                        >
                        </el-time-picker>
                    </el-form-item>
                    <el-form-item label="会议地点" style="width: 50%">
                        <div class="flex" style="align-items: center">
                            <div
                                v-if="
                                    form.meetingLocation ===
                                    '项目管理信息化平台'
                                "
                            >
                                项目管理信息化平台
                            </div>
                            <el-select
                                v-else
                                v-model="form.meetingLocation"
                                placeholder="请选择会议地点"
                                style="width: 100%"
                                maxlength="50"
                                multiple
                            >
                                <el-option
                                    v-for="item in roomData"
                                    :label="item.label"
                                    :value="item.value"
                                    :key="item.value"
                                ></el-option>
                            </el-select>
                            <el-button
                                v-if="
                                    form.meetingLocation !==
                                    '项目管理信息化平台'
                                "
                                type="primary"
                                class="add-room"
                                @click="handleBookRoom"
                                >预定会议室</el-button
                            >
                        </div>
                    </el-form-item>
                </div>

                <div class="flex">
                    <el-form-item
                        label="会议组织人"
                        :rules="required"
                        prop="organizerAccount"
                        style="width: 50%"
                    >
                        <PeopleSelector
                            v-model="form.organizerAccount"
                            :isMultipled="false"
                            ref="organizerAccount"
                            :options="externalOptions"
                            @input="
                                () => {
                                    if (!this.form.minutesWriterAccount) {
                                        this.form.minutesWriterAccount =
                                            this.form.organizerAccount;
                                    }
                                }
                            "
                        ></PeopleSelector>
                    </el-form-item>
                    <el-form-item
                        label="主持人"
                        :rules="required"
                        prop="hostAccount"
                        style="width: 50%"
                    >
                        <PeopleSelector
                            v-model="form.hostAccount"
                            :isMultipled="false"
                            ref="hostAccount"
                            :options="externalOptions"
                            @input="
                                () => {
                                    if (!this.form.minutesReviewerAccount) {
                                        this.form.minutesReviewerAccount =
                                            this.form.hostAccount;
                                    }
                                }
                            "
                        ></PeopleSelector>
                    </el-form-item>
                </div>
                <div class="flex">
                    <el-form-item
                        label="会议纪要编制人"
                        :rules="required"
                        prop="minutesWriterAccount"
                        style="width: 50%"
                    >
                        <PeopleSelector
                            :options="externalOptions"
                            v-model="form.minutesWriterAccount"
                            :isMultipled="false"
                            ref="minutesWriterAccount"
                        ></PeopleSelector>
                    </el-form-item>
                    <el-form-item
                        label="会议纪要审核人"
                        :rules="required"
                        prop="minutesReviewerAccount"
                        style="width: 50%"
                    >
                        <PeopleSelector
                            :options="externalOptions"
                            v-model="form.minutesReviewerAccount"
                            :isMultipled="false"
                            ref="minutesReviewerAccount"
                        ></PeopleSelector>
                    </el-form-item>
                </div>
                <div class="flex" v-if="meetingClass === '评审会议'">
                    <el-divider>评委清单</el-divider>
                </div>
                <div v-if="meetingClass === '评审会议'">
                    <div
                        v-for="(item, index) in form.reviewerList"
                        :key="index"
                    >
                        <el-form
                            ref="reviewerForm"
                            :model="item"
                            label-width="85px"
                            class="review-form"
                        >
                            <div class="flex wrap">
                                <el-form-item
                                    label="评委角色"
                                    :rules="required"
                                    prop="userRole"
                                >
                                    <el-select
                                        v-model="item.userRole"
                                        placeholder=""
                                        style="width: 240px"
                                        filterable
                                    >
                                        <el-option
                                            v-for="item in roleOptions"
                                            :label="item.paramName"
                                            :value="item.paramCode"
                                            :key="item.paramCode"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item
                                    label="评委姓名"
                                    :rules="required"
                                    prop="userAccount"
                                >
                                    <PeopleSelector
                                        :options="externalOptions"
                                        v-model="item.userAccount"
                                        :isMultipled="false"
                                        :ref="`reviewer${index}`"
                                        style="width: 120px"
                                    ></PeopleSelector>
                                </el-form-item>
                                <el-form-item
                                    label="参会情况"
                                    :rules="required"
                                    prop="attendanceStatus"
                                >
                                    <el-select
                                        v-model="item.attendanceStatus"
                                        @input="
                                            () => {
                                                item.replaceUserAccount = '';
                                                item.replaceUserName = '';
                                            }
                                        "
                                        style="width: 160px"
                                    >
                                        <el-option
                                            v-for="item in CONSTANTS.MEETING_ATTEND"
                                            :label="item"
                                            :value="item"
                                            :key="item"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item
                                    label="替代人"
                                    v-if="
                                        item.attendanceStatus === '请假' ||
                                        item.attendanceStatus ===
                                            '指派他人参会决策'
                                    "
                                    :rules="required"
                                    prop="replaceUserAccount"
                                >
                                    <PeopleSelector
                                        :options="externalOptions"
                                        v-model="item.replaceUserAccount"
                                        :isMultipled="false"
                                        :ref="`reviewerAlternative${index}`"
                                        style="width: 120px"
                                    ></PeopleSelector>
                                </el-form-item>
                                <el-button-group style="margin-left: auto">
                                    <el-button
                                        type="danger"
                                        icon="el-icon-minus"
                                        @click="decrease(index, 'reviewerList')"
                                        :disabled="
                                            form.reviewerList.length === 1
                                        "
                                    ></el-button>
                                    <el-button
                                        type="primary"
                                        icon="el-icon-plus"
                                        @click="increase('reviewerList')"
                                    ></el-button>
                                </el-button-group>
                            </div>
                        </el-form>
                    </div>
                </div>
                <div class="flex">
                    <el-divider
                        >参会人员清单（会议组织人、主持人无需再次录入）</el-divider
                    >
                    <el-button
                        type="text"
                        @click="hanleAddByDepartment('participantList')"
                        class="add-department-button"
                        >按部门添加</el-button
                    >
                </div>
                <div v-for="(item, index) in form.participantList" :key="index">
                    <el-form
                        ref="participantForm"
                        :model="item"
                        label-width="125px"
                    >
                        <div class="flex wrap">
                            <el-form-item
                                label="人员姓名"
                                prop="userAccount"
                                style="width: 520px"
                            >
                                <PeopleSelector
                                    :options="externalOptions"
                                    v-model="item.userAccount"
                                    :isMultipled="false"
                                    :ref="`participant${index}`"
                                ></PeopleSelector>
                            </el-form-item>
                            <el-form-item
                                label="参会情况"
                                prop="attendanceStatus"
                            >
                                <el-select
                                    v-model="item.attendanceStatus"
                                    @input="
                                        () => {
                                            item.replaceUserAccount = '';
                                            item.replaceUserName = '';
                                        }
                                    "
                                    style="width: 160px"
                                >
                                    <el-option
                                        v-for="item in CONSTANTS.MEETING_ATTEND"
                                        :label="item"
                                        :value="item"
                                        :key="item"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item
                                label="替代人"
                                v-if="
                                    item.attendanceStatus === '请假' ||
                                    item.attendanceStatus === '指派他人参会决策'
                                "
                                prop="replaceUserAccount"
                                style="margin-left: -40px"
                            >
                                <PeopleSelector
                                    :options="externalOptions"
                                    v-model="item.replaceUserAccount"
                                    :isMultipled="false"
                                    :ref="`participantAlternative${index}`"
                                    style="width: 120px"
                                ></PeopleSelector>
                            </el-form-item>
                            <el-button-group style="margin-left: auto">
                                <el-button
                                    type="danger"
                                    icon="el-icon-minus"
                                    @click="decrease(index, 'participantList')"
                                ></el-button>
                                <el-button
                                    type="primary"
                                    icon="el-icon-plus"
                                    @click="increase('participantList')"
                                ></el-button>
                            </el-button-group>
                        </div>
                    </el-form>
                </div>
                <el-divider>邮件抄送人</el-divider>
                <el-form-item label="邮件抄送人">
                    <PeopleSelector
                        :options="externalOptions"
                        v-model="form.whiteList"
                        ref="whiteList"
                        style="width: 700px"
                    ></PeopleSelector>
                </el-form-item>
                <el-divider></el-divider>
                <el-form-item
                    label="关联项目"
                    :rules="
                        meetingClass === '评审会议' ||
                        this.form.meetingType === 'DCP评审'
                            ? required
                            : []
                    "
                    prop="assProjectIdList"
                >
                    <el-select
                        v-model="form.assProjectIdList"
                        placeholder="请选择关联项目"
                        filterable
                        multiple
                        class="related-projects"
                    >
                        <el-option
                            v-for="item in relatedProjectsOptions"
                            :key="item.projectId"
                            :label="item.projectName"
                            :value="item.projectId"
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="会议议程及事项" prop="meetingAgenda">
                    <el-input
                        v-model="form.meetingAgenda"
                        placeholder="请输入会议议程及事项"
                        type="textarea"
                        :rows="5"
                        maxlength="500"
                    ></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button
                    type="primary"
                    @click="save('发送邮件')"
                    v-show="!isHideSendingButton"
                    >保存并发送通知</el-button
                >
                <el-button type="primary" @click="save('保存')"
                    >保 存</el-button
                >
            </div>
        </el-dialog>
        <BookSelectedRoomDialog
            :visible.sync="BookSelectedRoomDialogVisible"
            :name="form.meetingTitle"
            @success="handleBookSuccess"
        />

        <!-- 复制会议弹窗 -->
        <el-dialog
            title="复制会议"
            :visible.sync="copyDialogVisible"
            width="800px"
            :before-close="closeCopyDialog"
        >
            <el-select
                v-model="historyMeeting"
                placeholder="请选择会议"
                filterable
                class="related-projects"
                style="width: 100%"
            >
                <el-option
                    v-for="item in historyMeetingOptions"
                    :key="item.meetingId"
                    :label="item.meetingTitle"
                    :value="item.meetingId"
                >
                </el-option>
            </el-select>
            <div slot="footer" class="dialog-footer">
                <el-button @click="closeCopyDialog">取 消</el-button>
                <el-button type="primary" @click="confirmCopyMeeting"
                    >确 定</el-button
                >
            </div>
        </el-dialog>
        <PeopleSelectorWithOrg
            :visible.sync="peopleSelectorWithOrgDialogVisible"
            :roleType="currentRoleType"
            @confirm="handleAddPeopleByOrg"
        ></PeopleSelectorWithOrg>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';
import moment from 'moment';
import { form, handleFormData, handleBackendData, validateForm } from './init';
import BookSelectedRoomDialog from '../BookSelectedRoomDialog.vue';
import PeopleSelectorWithOrg from '../PeopleSelectorWithOrg.vue';

export default {
    name: 'MeetingUpdate',
    components: {
        PeopleSelector,
        BookSelectedRoomDialog,
        PeopleSelectorWithOrg
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        meetingId: {
            type: String,
            default: ''
        },
        // 外部传入的会议地点
        address: {
            type: Array,
            default: () => []
        }
    },

    data() {
        return {
            CONSTANTS,
            form: this.$tools.cloneDeep(form),
            // 相关项目的下拉列表选项
            relatedProjectsOptions: [],
            // 关联上次会议的下拉列表选项
            relatedMeetingOptions: [],
            // 评委角色选择下拉框
            roleOptions: [],
            // 线上评审意见的数据
            onlineReviewData: [],
            isHideSendingButton: false,
            required: {
                required: true,
                message: ' ',
                trigger: ['change', 'blur']
            },
            BookSelectedRoomDialogVisible: false,
            // 所有会议室
            roomData: [],
            // 复制会议弹窗显示状态
            copyDialogVisible: false,
            // 历史会议下拉框选项
            historyMeetingOptions: [],
            historyMeeting: '',
            peopleSelectorWithOrgDialogVisible: false,
            currentRoleType: ''
        };
    },
    computed: {
        // 会议归类
        meetingClass() {
            if (
                this.form.meetingType === '一级TR评审' ||
                this.form.meetingType === '二级TR评审'
            ) {
                return '评审会议';
            }
            return '一般会议';
        },
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update');
                this.$emit('update:visible', value);
            }
        },
        isEdit() {
            return this.title === '编辑会议';
        },
        // 人员选择的下拉框选项（包x含外部人员）
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getRelatedProjectsOptions();
                this.getMeetingOptions();
                this.getRoleOptions();
                this.getRoomData();
                if (this.isEdit) {
                    this.getMeetingInfo();
                    this.getOnlineReviewData();
                } else {
                    this.form = this.$tools.cloneDeep(form);
                }
                // 如果外部预定了会议室，使用外部传入的会议地点
                if (this.address.length > 0) {
                    this.form.meetingLocation = this.address;
                }
            }
        }
    },
    methods: {
        handleBookSuccess(address) {
            this.form.meetingLocation = address;
        },
        /**
         * 按部门添加人员
         * @param {String} peopleList 评委列表或者参会人员列表
         * @param {String} type 评委或者参会人员
         */
        handleAddPeopleByOrg(peopleList, type) {
            const meetingRole = type === 'reviewerList' ? '评委' : '参会人员';
            const userRole =
                type === 'reviewerList' ? '评审决策人' : '参会人员';
            this.form[type].push(
                ...peopleList.map((i) => ({
                    // 会议角色
                    meetingRole,
                    // 评委角色
                    userRole,
                    // 评委姓名
                    userAccount: i.account,
                    // 参会情况
                    attendanceStatus: '参会',
                    // 替代人
                    replaceUserAccount: ''
                }))
            );
        },
        closeDialog() {
            this.form = this.$tools.cloneDeep(form);
            this.resetValidate();
            this.dialogVisible = false;
        },
        async getRoomData() {
            const api = this.$service.feature.meetingRoom.getRoomList;
            const params = {};
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.roomData = res.body.map((i) => ({
                    label: i.name,
                    value: i.name
                }));
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 评审组织形式修改时的变更
         * @param {String} value 当前值
         */
        handleIsOnlineChange(value) {
            this.form.date = '';
            this.form.timeRange = '';
            if (value === '线上') {
                this.form.date = moment(new Date()).format('YYYY-MM-DD');
                this.form.meetingLocation = '项目管理信息化平台';
            } else {
                this.form.meetingLocation = [];
            }
        },
        /**
         * 删除项
         * @param {Number} index 序号
         * @param {String} type 评委列表或者参会人员列表
         */
        decrease(index, type) {
            this.form[type].splice(index, 1);
        },
        /**
         * 新增项
         * @param {String} type 评委列表或者参会人员列表
         */
        increase(type) {
            const meetingRole = type === 'reviewerList' ? '评委' : '参会人员';
            const userRole =
                type === 'reviewerList' ? '评审决策人' : '参会人员';
            this.form[type].push({
                // 会议角色
                meetingRole,
                // 评委角色
                userRole,
                // 评委姓名
                userAccount: '',
                // 参会情况
                attendanceStatus: '参会',
                // 替代人
                replaceUserAccount: ''
            });
        },
        /**
         * 创建/编辑会议
         * @param {String} type 按钮类型
         */
        async save(type) {
            const valid = await validateForm(this);
            if (!valid) {
                this.$message.warning('请填写必填项');
                return;
            }
            const inputData = this.$tools.cloneDeep(this.form);
            const { date, timeRange, organizeForm } = inputData;
            // 会议时间如果只有日期，或者只有时间范围，则不合法
            let isValidTime = true;
            if (date || (Array.isArray(timeRange) && timeRange[0])) {
                isValidTime = date && Array.isArray(timeRange) && timeRange[0];
            }

            if (!isValidTime && organizeForm === '线下') {
                this.$message.warning('请输入完整的会议时间');
                return;
            }
            let data = handleFormData(inputData, this);
            const leader = data.meetingPartRelateList.filter(
                (i) => i.userRole === '评审决策人'
            );
            if (leader.length === 0 && this.meetingClass === '评审会议') {
                this.$message.warning('至少需要一位评审决策人');
                return;
            } else if (leader.length > 2) {
                this.$message.warning('评审决策人不能超过两位');
                return;
            }
            let api;
            let opration;
            if (this.isEdit) {
                api = this.$service.feature.meeting.update;
                opration = '编辑会议';
            } else {
                api = this.$service.feature.meeting.create;
                opration = '创建会议';
            }
            if (type === '发送邮件') {
                api = this.$service.feature.meeting.sendEmail;
                if (data.meetingInfo.meetingStatus === '已取消') {
                    data = this.handleRebornCanceledMeeting(data);
                }
                data = { ...data, operateName: opration };
            }
            await this.validateDuplicateJudges(data);
            try {
                const res = await api(data);
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err, '会议信息更新失败');
            }
        },
        /**
         * 已经取消的会议重新开始的时候的处理
         * @param {Object} data 会议数据
         * @returns {Object} 处理后的会议数据
         */
        handleRebornCanceledMeeting(data) {
            data.meetingInfo.meetingStatus = '待召开';
            data.meetingInfo.minutesReviewDate = null;
            data.meetingInfo.minutesStatus = '无纪要';
            data.meetingInfo.minutesWriteDate = null;
            data.meetingInfo.nextStep = null;
            data.meetingInfo.meetingConclusion = null;
            data.meetingInfo.meetingConclusionView = null;
            data.meetingInfo.cancelReason = null;
            return data;
        },
        /**
         * 获取所有项目用于下拉框选项
         */
        async getRelatedProjectsOptions() {
            try {
                const api = this.$service.department.naturalResources;
                const res = await api.getProjectselect();
                if (res.head.code === '000000') {
                    this.relatedProjectsOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 获取所有会议用于下拉框选项
         */
        async getMeetingOptions() {
            const api = this.$service.feature.meeting.getHistoryMeetingOptions;
            const params = { type: '首次会议' };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.relatedMeetingOptions =
                        res.body.filter(
                            (i) => i.meetingId !== this.meetingId
                        ) || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 获取所有会议用于下拉框选项
         */
        async getRoleOptions() {
            try {
                const api = this.$service.project.finance;
                const res = await api.getSelectOptions({
                    paramName: '会议评委角色',
                    paramType: ''
                });
                if (res.head.code === '000000') {
                    this.roleOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        /**
         * 获取会议信息
         * @param {String} historyMeetingId 历史会议id
         * @param {String} type 类型
         */
        async getMeetingInfo(historyMeetingId, type) {
            const api = this.$service.feature.meeting.getMeetingInfo;
            try {
                const res = await api({
                    meetingId: historyMeetingId || this.meetingId
                });
                if (res.head.code === '000000') {
                    this.form = handleBackendData(res.body);
                    // 如果是复制会议，需要特殊处理
                    if (this.type === 'copy') {
                        this.handleCopyMeeting();
                    }
                    this.isHideSendingButton =
                        this.handleIsHideSendingMailButton();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        handleCopyMeeting() {
            this.form.id = '';
            this.form.meetingStatus = '待召开';
            this.form.minutesReviewDate = null;
            this.form.minutesStatus = '无纪要';
            this.form.minutesWriteDate = null;
            this.form.nextStep = null;
            this.form.meetingConclusion = null;
            this.form.meetingConclusionView = null;
            this.form.cancelReason = null;
        },
        /**
         * 重置
         * @param {Function} done 关闭弹窗的动作
         */
        reset(done) {
            this.form = this.$tools.cloneDeep(form);
            this.resetValidate();
            done();
        },
        /**
         * 重置表单校验
         */
        resetValidate() {
            this.$refs.form.resetFields();

            this.$refs.reviewerForm &&
                this.$refs.reviewerForm.forEach((f) => {
                    f.resetFields();
                });
            this.$refs.participantForm.forEach((f) => {
                f.resetFields();
            });
        },
        /**
         * 会议类型变更时的回调
         */
        handleMeetingTypeChange() {
            if (this.meetingClass === '评审会议') {
                this.form.organizeForm = '线下';
            } else {
                this.form.organizeForm = '';
            }
            this.form.meetingLocation = [];
        },
        /**
         * 获取线上评审意见
         */
        async getOnlineReviewData() {
            const api = this.$service.feature.onlineReview.getInfo;
            try {
                const res = await api({
                    meetingId: this.meetingId
                });
                if (res.head.code === '000000') {
                    this.onlineReviewData = res.body;
                    this.isHideSendingButton =
                        this.handleIsHideSendingMailButton();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 会议邮件按钮是否要隐藏
         * @returns {Boolean} 是否发邮件按钮要隐藏
         */
        handleIsHideSendingMailButton() {
            if (
                this.form.organizeForm === '线下' &&
                Array.isArray(this.form.timeRange) &&
                this.form.timeRange[0]
            ) {
                const startTimeMoment = moment(
                    `${this.form.date} ${this.form.timeRange[0] || ''}:00`,
                    'YYYY-MM-DD HH:mm:ss'
                );
                const now = moment();
                // 检查 moment 对象是否有效
                if (!startTimeMoment.isValid()) {
                    console.error('无效的时间格式');
                    // 时间格式无效时，隐藏按钮
                    return false;
                }
                return startTimeMoment.isBefore(now);
            } else if (this.form.organizeForm === '线上') {
                // 没有会议id，代表是创建的线上会议，则一定没有开始
                if (!this.meetingId) return false;
                // 评审意见为空，直接认为会议未开始
                if (this.onlineReviewData.length === 0) {
                    return false;
                }

                let valid = false;
                this.onlineReviewData.forEach((i) => {
                    // 只要有任意评委写了结论，就代表会议开始了，这时候就隐藏发邮件按钮
                    if (i.reviewConclusion) {
                        valid = true;
                    }
                });

                return valid;
            }
        },
        /**
         * 校验会议信息中评委参会时间是否冲突
         * @param {Object} data 会议信息
         */
        async validateDuplicateJudges(data) {
            const api = this.$service.feature.meeting.validateDuplicateJudges;
            try {
                const res = await api(data);
                if (res.head.code === '991003') {
                    await this.$tools.confirm(`${res.head.message},确认提交吗`);
                }
            } catch (error) {
                console.error(error);
                throw error;
            }
        },
        handleBookRoom() {
            this.BookSelectedRoomDialogVisible = true;
        },
        handleMeetingCopy() {
            this.getHistoryMeetingOptions();
            this.copyDialogVisible = true;
        },
        closeCopyDialog() {
            this.historyMeeting = '';
            this.copyDialogVisible = false;
        },
        confirmCopyMeeting() {
            this.getMeetingInfo(this.historyMeeting, 'copy');
            this.closeCopyDialog();
        },
        /**
         * 获取历史会议选项
         */
        async getHistoryMeetingOptions() {
            const api = this.$service.feature.meeting.getHistoryMeetingOptions;
            const params = { type: '复制会议' };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.historyMeetingOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        hanleAddByDepartment(roleType) {
            this.currentRoleType = roleType;
            this.peopleSelectorWithOrgDialogVisible = true;
        }
    }
};
</script>

<style lang="scss" scoped>
.mb-12 {
    margin-bottom: 12px;
}
.flex {
    display: flex;
}
.wrap {
    flex-wrap: wrap;
}
.meeting-material {
    margin-bottom: 12px;
}

.related-projects {
    width: 700px;
}

.related-meeting-form {
    width: 50%;
    .related-meeting-input {
        width: 100%;
    }
}

.is-first {
    margin-bottom: 18px !important;
    width: 50%;
}

.time-picker {
    margin-left: 10px !important;
    width: 200px !important;
    height: 40px !important;
    ::v-deep .el-range-separator {
        line-height: 34px !important;
    }
}

.review-form {
    margin-left: 40px;
}

.add-room {
    margin-left: 10px;
}

::v-deep.el-radio input[aria-hidden='true'] {
    display: none !important;
}

::v-deep.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
    .el-radio__inner {
    box-shadow: none !important;
}

::v-deep.form .el-form-item__label {
    font-weight: bold;
    padding-right: 10px;
    width: fit-content;
}

.room-occupation-table {
    margin: 20px 0;
}

.room-booking-table {
    .time-cell {
        width: 100%;
        height: 30px;
        cursor: pointer;
        border-radius: 2px;
        position: relative;

        &.available {
            background-color: #f0f9ff;
            border: 1px solid #e1f5fe;

            &:hover {
                background-color: #e3f2fd;
            }
        }

        &.occupied {
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
        }
    }
}

.meeting-popover {
    .meeting-info-item {
        margin-bottom: 8px;

        &:last-child {
            margin-bottom: 0;
        }

        .label {
            font-weight: bold;
            color: #303133;
        }

        .value {
            color: #606266;
        }
    }
}
.copy-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
}
.copy-icon {
    width: 20px;
    height: 20px;
    margin-left: 15px;
    cursor: pointer;
    &:hover {
        transform: scale(1.1);
    }
}
</style>

<style>
.feature-meeting--meeting-update .el-dialog__body {
    padding: 20px 10px;
}
</style>
